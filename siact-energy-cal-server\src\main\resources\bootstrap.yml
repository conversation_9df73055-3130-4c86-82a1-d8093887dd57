spring:
  application:
    name: siact-energy-cal-server
  cloud:
    nacos:
#      server-addr: 192.168.0.78:8848
      server-addr: 192.100.30.102:8848
#      server-addr: 172.17.181.197:8848
      config:
        server-addr: ${spring.cloud.nacos.server-addr}
        file-extension: yml
        prefix: ${spring.application.name}
        group: DEFAULT_GROUP
        extension-configs:
          - data-id: siact-energy-cal-mysql.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: siact-energy-cal-knife4j.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: siact-energy-cal-logging.yml
            group: DEFAULT_GROUP
            refresh: true
        namespace: energy_cal_all
      discovery:
        server-addr: ${spring.cloud.nacos.server-addr}

