package com.siact.energy.cal.server.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "calculation.planner")
@Data
public class CalculationProperties {
    /** 当查询时长超过此天数(且为等间隔查询)，触发时间分片 */
    private int timeShardThresholdDays = 7;
    /** 当一次查询的指标数量超过此数量时，触发指标分片 */
    private int metricShardThresholdCount = 50;
    /** 每个指标分片最多包含多少个指标 */
    private int metricShardSize = 50;
    /** 应用层同时最多向数据库发起多少个查询子任务 */
    private int maxConcurrentQueries = 16;
}