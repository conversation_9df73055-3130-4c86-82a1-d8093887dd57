package com.siact.energy.cal.server.core.service;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.enums.TimeQueryType;
import com.siact.energy.cal.common.pojo.vo.energycal.DataIntervalQueryVo;
import com.siact.energy.cal.common.pojo.vo.energycal.DataPointQueryVo;
import com.siact.energy.cal.common.util.utils.DateUtils;
import com.siact.energy.cal.server.core.calculator.CalculatorManager;
import com.siact.energy.cal.server.core.utils.ResultAssembler;
import com.siact.energy.cal.server.core.pojo.CalculationResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @Package com.siact.energy.cal.server.service.energycal
 * @description: 统一计算服务
 * @create 2025/8/8 16:04
 */

@Service
@Slf4j
public class CalculateService {

    @Autowired
    private CalculatorManager calculateManager;

    @Autowired
    private ResultAssembler resultAssembler;
    public List<DataIntervalQueryVo> calculateEquallySpacedTimeData(TimeQueryDTO timeQueryDTO) {

        try {

            // 1.参数验证和预处理
            validateTimeQuery(timeQueryDTO);
            // 2.统一计算
            CompletableFuture<CalculationResult> calculationFuture =
                    calculateManager.calculateEquallySpacedTimeData(timeQueryDTO);
            // 3.等待计算完成
            CalculationResult calculationResult = calculationFuture.get(60, TimeUnit.SECONDS);
            // 4.结果过滤与返回
            // 在控制器层进行结果组装，使用原始请求指标进行过滤
            List<DataIntervalQueryVo> result = resultAssembler.assembleResults(
                    calculationResult.getGlobalResults(), timeQueryDTO, calculationResult.getTimeWindows());

            return result;
        } catch (Exception e) {
            throw new BizException("计算失败:"+ e.getMessage());
        }


    }

    public List<DataPointQueryVo> calculateIntervalTimeData(TimeQueryDTO timeQueryDTO) {
        try {

            // 1.参数验证和预处理
            validateTimeQuery(timeQueryDTO);
            // 2.统一计算
            CompletableFuture<CalculationResult> calculationFuture =
                    calculateManager.calculateIntervalTimeData(timeQueryDTO);
            // 3.等待计算完成
            CalculationResult calculationResult = calculationFuture.get(60, TimeUnit.SECONDS);
            // 4.结果过滤与返回
            List<DataPointQueryVo> result = resultAssembler.assembleTimeIntervalResults(
                    calculationResult.getGlobalResults(), timeQueryDTO);
            return result;
        } catch (Exception e) {
            throw new BizException("计算失败:"+ e.getMessage());
        }

    }

    /**
     * 时间断面数据计算
     *
     * @param queryDTO 查询参数
     * @return 计算结果
     */
    public List<DataPointQueryVo> calculateTimeSliceData(TimeQueryDTO queryDTO) {

        try {
            log.info("开始统一时间断面计算: 指标数量={}, 目标时间点={}",
                    queryDTO.getDataCodes().size(), queryDTO.getStartTime());
            // 1.参数验证与预处理
        validateTimeQuery(queryDTO);
            // 2.统一计算
            CompletableFuture<CalculationResult> calculationFuture =
                    calculateManager.calculateTimeSliceData(queryDTO);
            // 3.等待计算完成
            CalculationResult calculationResult = calculationFuture.get(60, TimeUnit.SECONDS);
            // 4.结果过滤与返回 - 使用专门的时间断面结果组装方法
            List<DataPointQueryVo> result = resultAssembler.assembleTimeSliceResults(
                    calculationResult.getGlobalResults(), queryDTO.getDataCodes(), queryDTO.getStartTime());
            return result;

    } catch (Exception e) {
            throw new BizException("计算失败:"+ e.getMessage());
    }

    }


    private void validateTimeQuery(TimeQueryDTO queryDTO) {
        if(TimeQueryType.TIMESLICE.equals(queryDTO.getQueryType())){
            validateAndPrepareParams(queryDTO);
        }else{
            // 1. 基础参数验证
            validateBasicParameters(queryDTO);
            // 2. 时间格式和范围验证
            validateTimeParameters(queryDTO);
            // 3. 等时间间隔查询的特殊验证
            if (queryDTO.isEquallySpacedQuery()) {
                validateEquallySpacedParameters(queryDTO);
            }
        }
        // 4.时间单位转化
        queryDTO.setTsUnit(convertTsUnit(queryDTO.getTsUnit()));
    }

    /**
     * 时间截面参数验证和准备参数
     */
    private void validateAndPrepareParams(TimeQueryDTO queryDTO) {
        if (CollectionUtils.isEmpty(queryDTO.getDataCodes())) {
            throw new BizException("查询属性不能为空");
        }

        // 处理时间参数
        if (StringUtils.isBlank(queryDTO.getStartTime())) {
            queryDTO.setStartTime(DateUtils.getCurrentimeStr());
        }

        if (!DateUtils.isTimeFormatValid(queryDTO.getStartTime(), DateUtils.YMD_HMS)) {
            throw new BizException("时间格式不正确");
        }
    }


    private void validateBasicParameters(TimeQueryDTO queryDTO) {
        // 验证数据编码列表
        if (CollectionUtils.isEmpty(queryDTO.getDataCodes())) {
            throw new BizException("查询属性不能为空");
        }

        // 验证时间参数存在性
        if (StrUtil.isBlank(queryDTO.getStartTime())
                || StrUtil.isBlank(queryDTO.getEndTime())) {
            throw new BizException("开始时间和结束时间不能为空");
        }
    }

    private void validateTimeParameters(TimeQueryDTO queryDTO) {
        String startTime = queryDTO.getStartTime();
        String endTime = queryDTO.getEndTime();

        // 验证时间格式
        if (!DateUtils.isTimeFormatValid(startTime, DateUtils.YMD_HMS)) {
            throw new BizException("开始时间格式不正确，应为yyyy-MM-dd HH:mm:ss格式");
        }

        if (!DateUtils.isTimeFormatValid(endTime, DateUtils.YMD_HMS)) {
            throw new BizException("结束时间格式不正确，应为yyyy-MM-dd HH:mm:ss格式");
        }

        // 验证时间范围
        if (startTime.compareTo(endTime) > 0) {
            throw new BizException("开始时间不能大于结束时间");
        }
    }

    private void validateEquallySpacedParameters(TimeQueryDTO queryDTO) {
        Integer interval = queryDTO.getInterval();
        String tsUnit = queryDTO.getTsUnit();

        // 验证步长和单位的存在性
        if (interval == null || StrUtil.isBlank(tsUnit)) {
            throw new BizException("步长和步长单位必须同时设置");
        }

        // 验证步长值
        if (interval <= 0) {
            throw new BizException("步长必须大于0");
        }

        // 验证并转换时间单位
        String tsUnitConverted = convertTsUnit(tsUnit);
        if (StrUtil.isBlank(tsUnitConverted)) {
            throw new BizException("步长单位不正确，支持的单位有:(Y:年;M:月;D:日;H:小时;MIN:分)");
        }
    }

    public static String convertTsUnit(String tsUnit) {
        if (tsUnit == null) {
            return null;
        }
        String result = null;
        switch (tsUnit) {
            case "Y":
                result = "y";
                break;
            case "M":
                result = "n";
                break;
            case "D":
                result = "d";
                break;
            case "H":
                result = "h";
                break;
            case "MIN":
                result = "m";
                break;
        }
        return result;
    }
}
