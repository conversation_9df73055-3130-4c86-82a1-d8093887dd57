package com.siact.energy.cal.server.core.calculator;

import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.enums.ConstantBase;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.server.core.pojo.AggregateFormulaInfo;
import com.siact.energy.cal.server.core.pojo.TimeWindow;
import com.siact.energy.cal.server.core.service.FormulaCacheService;
import com.siact.energy.cal.server.core.utils.TimeWindowGenerator;
import com.siact.energy.cal.server.service.energycal.DataBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @Package com.siact.energy.cal.server.core
 * @description: 聚合指标计算器
 * <AUTHOR>
 * @create 2025/8/8 15:44
 */

@Slf4j
@Service
public class AggIndicatorCalculator {
    @Autowired
    private DataBaseService dataBaseService;

    @Autowired
    private TimeWindowGenerator timeWindowGenerator;

    @Autowired
    private FormulaCacheService formulaCacheService;
    // 使用 ApplicationContext 解决循环依赖 (Agg -> Base)
    @Autowired
    private ApplicationContext applicationContext;
    private BaseIndicatorCalculator baseIndicatorCalculator;

    @PostConstruct
    public void init() {
        this.baseIndicatorCalculator = applicationContext.getBean(BaseIndicatorCalculator.class);
    }


    // 专用于聚合指标计算的线程池
    private final ForkJoinPool aggregateCalculationPool = new ForkJoinPool(
            Math.min(Runtime.getRuntime().availableProcessors(), 8));

    // 支持的聚合函数类型
    private static final Set<String> SUPPORTED_AGG_FUNCTIONS = new HashSet<>(Arrays.asList(
            "sum", "avg", "max", "min", "count", "first", "last", "diff"
    ));

    /**
     * 计算聚合指标 - 主入口方法
     * 优化策略：按聚合函数类型分组，数据库内批量执行聚合运算
     */
    public CompletableFuture<Void> calculateAggregateIndicators(
            List<String> aggregateIndicators,
            List<TimeWindow> timeWindows,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults) {

        return CompletableFuture.runAsync(() -> {
            if (CollectionUtils.isEmpty(aggregateIndicators)) return;

            StopWatch stopWatch = new StopWatch("聚合指标计算(重构版)");
            stopWatch.start();
            try {
                Map<String, Map<String, String>> aggTypeMap = parseAggregateFormulas(aggregateIndicators);
                executeAggregateCalculations(aggTypeMap, timeWindows, queryDTO, dataSourceVo, globalResults);
            } catch (Exception e) {
                log.error("聚合指标计算失败", e);
                throw new BizException("聚合指标计算失败", e);
            } finally {
                stopWatch.stop();
                log.info(stopWatch.prettyPrint());
            }
        }, aggregateCalculationPool);
    }

    private Map<String, Map<String, String>> parseAggregateFormulas(List<String> aggregateIndicators) {
        Map<String, Map<String, String>> aggTypeMap = new HashMap<>();
        Map<String, String> formulaMap = formulaCacheService.batchGetFormulas(aggregateIndicators, ConstantBase.RULECOLID_COMMON);
        for (String indicator : aggregateIndicators) {
            String formula = formulaMap.get(indicator);
            if (StringUtils.isBlank(formula)) continue;
            AggregateFormulaInfo formulaInfo = parseAggregateFormula(formula);
            if (formulaInfo != null && SUPPORTED_AGG_FUNCTIONS.contains(formulaInfo.getFunctionName())) {
                aggTypeMap.computeIfAbsent(formulaInfo.getFunctionName(), k -> new HashMap<>())
                        .put(formulaInfo.getSourceProperty(), indicator);
            }
        }
        return aggTypeMap;
    }

    private void executeAggregateCalculations(
            Map<String, Map<String, String>> aggTypeMap,
            List<TimeWindow> timeWindows,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults) {

        Set<String> highDensityFuncs = new HashSet<>(Arrays.asList("sum", "avg", "max", "min", "count"));

        // 1. 分离任务
        Map<String, Map<String, String>> highDensityTasks = new HashMap<>();
        Map<String, Map<String, String>> lowDensityTasks = new HashMap<>();
        aggTypeMap.forEach((func, mapping) -> {
            if (highDensityFuncs.contains(func.toLowerCase())) {
                highDensityTasks.put(func, mapping);
            } else {
                lowDensityTasks.put(func, mapping);
            }
        });

        // 2. 首先，并行执行所有低密度任务
        executeParallelTasks(lowDensityTasks, timeWindows, queryDTO, dataSourceVo, globalResults);

        // 3. 然后，集中处理所有高密度任务
        if (!highDensityTasks.isEmpty()) {
            //补算

            // a. 统一收集所有需要补算的源指标
            prepareHighDensityData(highDensityTasks, queryDTO, dataSourceVo);
            // c. 补算完成后，并行执行所有高密度聚合查询
            executeParallelTasks(highDensityTasks, timeWindows, queryDTO, dataSourceVo, globalResults);
        }
    }

    /**
     * 统一准备高密度数据：使用固定的1分钟粒度检查数据完整性，并在需要时一次性完成所有补算。
     */
    private void prepareHighDensityData(
            Map<String, Map<String, String>> highDensityTasks,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo) {

        // 收集所有高密度任务的源指标
        Set<String> allSourceProps = highDensityTasks.values().stream()
                .flatMap(mapping -> mapping.keySet().stream())
                .collect(Collectors.toSet());

        if (allSourceProps.isEmpty()) return;

        // 创建一个全新的、独立的 DTO，专门用于完整性检查，粒度固定为1分钟
        TimeQueryDTO checkQueryDTO = new TimeQueryDTO();
        BeanUtils.copyProperties(queryDTO, checkQueryDTO); // 继承起止时间等
        checkQueryDTO.setInterval(1);
        checkQueryDTO.setTsUnit("m"); // 使用 'm'，确保与 findIncompleteIndicators 内部逻辑一致
        List<TimeWindow> timeWindowsList = timeWindowGenerator.generateTimeWindows(checkQueryDTO);
        //  使用这个专门的 DTO 进行检查
        List<String> incompleteIndicators = dataBaseService.findIncompleteIndicators(allSourceProps,timeWindowsList, checkQueryDTO, dataSourceVo);

        // 如果需要补算，则一次性完成
        if (!incompleteIndicators.isEmpty()) {
            log.info("检测到 {} 个源指标数据不完整(基于1分钟粒度检查)，触发统一的高密度补算...", incompleteIndicators.size());

            // 补算时，我们需要计算的指标是那些不完整的指标
            checkQueryDTO.setDataCodes(incompleteIndicators);


            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> recomputationResultMap = new ConcurrentHashMap<>();
            CompletableFuture<Void> recomputationFuture = baseIndicatorCalculator.calculateBaseIndicators(
                    incompleteIndicators,
                    timeWindowsList,
                    checkQueryDTO,
                    dataSourceVo,
                    recomputationResultMap
            );

            recomputationFuture.join(); // 同步等待补算完成
            log.info("统一的高密度数据补算完成。");
        } else {
            log.info("所有高密度任务的源指标数据完整(基于1分钟粒度检查)，无需补算。");
        }
    }

    /**
     * 为给定的指标列表，执行一次性的高密度补算
     */
    private void performRecomputation(
            Set<String> propsToRecompute,
            List<TimeWindow> timeWindows,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo) {

        log.info("检测到 {} 个源指标数据不完整，触发统一的高密度补算...", propsToRecompute.size());

        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> recomputationResultMap = new ConcurrentHashMap<>();
        CompletableFuture<Void> recomputationFuture = baseIndicatorCalculator.calculateBaseIndicators(
                new ArrayList<>(propsToRecompute), timeWindows, queryDTO, dataSourceVo, recomputationResultMap
        );

        recomputationFuture.join(); // 同步等待补算完成
        log.info("统一的高密度数据补算完成。");
    }

    /**
     * 通用的并行任务执行器
     */
    private void executeParallelTasks(
            Map<String, Map<String, String>> tasks,
            List<TimeWindow> timeWindows,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults) {

        if (tasks.isEmpty()) return;

        List<CompletableFuture<Void>> futures = tasks.entrySet().stream()
                .map(entry -> CompletableFuture.runAsync(() -> {
                    String functionName = entry.getKey();
                    Map<String, String> propMapping = entry.getValue();
                    try {
                        if ("diff".equals(functionName)) {
                            processDiffCalculation(globalResults, propMapping);
                        } else {
                            dataBaseService.processAggQuery(globalResults, functionName, propMapping, queryDTO, dataSourceVo);
                        }
                    } catch (Exception e) {
                        log.error("执行 {} 聚合计算任务失败", functionName, e);
                    }
                }, aggregateCalculationPool))
                .collect(Collectors.toList());

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }


    /**
     * 解析单个聚合公式
     * 支持格式：sum(property), avg(property), max(property) 等
     */
    private AggregateFormulaInfo parseAggregateFormula(String formula) {
        if (StringUtils.isBlank(formula)) {
            return null;
        }

        // 移除空格，保持原始大小写
        String cleanFormula = formula.trim();

        // 查找函数名和参数
        int openParen = cleanFormula.indexOf('(');
        int closeParen = cleanFormula.lastIndexOf(')');

        if (openParen == -1 || closeParen == -1 || openParen >= closeParen) {
            return null;
        }

        // 函数名转换为小写，属性编码保持原始大小写
        String functionName = cleanFormula.substring(0, openParen).trim().toLowerCase();
        String rawSourceProperty = cleanFormula.substring(openParen + 1, closeParen).trim();

        if (StringUtils.isBlank(functionName) || StringUtils.isBlank(rawSourceProperty)) {
            return null;
        }

        // 处理 @[property] 格式，提取纯净的属性编码（保持原始大小写）
        String sourceProperty = extractPropertyFromVariable(rawSourceProperty);
        if (StringUtils.isBlank(sourceProperty)) {
            log.warn("无法从公式参数中提取属性编码: {}", rawSourceProperty);
            return null;
        }

        return new AggregateFormulaInfo(functionName, sourceProperty);
    }

    /**
     * 从变量引用中提取属性编码
     * 处理格式：@[property_code] -> property_code
     */
    private String extractPropertyFromVariable(String variable) {
        if (StringUtils.isBlank(variable)) {
            return null;
        }

        String trimmed = variable.trim();

        // 处理 @[property_code] 格式
        if (trimmed.startsWith("@[") && trimmed.endsWith("]")) {
            String extracted = trimmed.substring(2, trimmed.length() - 1).trim();
            log.debug("从变量引用格式提取属性编码: {} -> {}", variable, extracted);
            return extracted;
        }

        // 如果不是 @[...] 格式，直接返回原值
        return trimmed;
    }

    /**
     * 差值计算（并行流式）
     * 直接使用globalResults中已有的数据，避免重复查询数据库
     * 统一使用并行流式计算，简化代码并提升性能
     *
     * @param globalResults 全局结果映射，包含所有已计算的指标数据
     * @param propMapping 属性映射，key为源指标，value为目标差值指标
     */
    private void processDiffCalculation(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults,
            Map<String, String> propMapping) {

        if (propMapping.isEmpty()) {
            log.debug("差值计算属性映射为空，跳过处理");
            return;
        }

        log.debug("开始并行流式差值计算，源指标数量: {}", propMapping.size());

        // 统一使用并行流式计算，简化代码并提升性能
        propMapping.entrySet().parallelStream()
                .forEach(entry -> {
                    String sourceIndicator = entry.getKey();
                    String targetIndicator = entry.getValue();

                    try {
                        ConcurrentHashMap<String, BigDecimal> sourceData = globalResults.get(sourceIndicator);

                        if (sourceData == null || sourceData.isEmpty()) {
                            log.warn("源指标 {} 的数据为空，跳过差值计算", sourceIndicator);
                            return;
                        }

                        // 使用流式差值计算
                        calculateDiffForIndicatorStream(sourceData, targetIndicator, globalResults);
                        log.debug("指标 {} -> {} 并行流式差值计算完成", sourceIndicator, targetIndicator);

                    } catch (Exception e) {
                        log.error("指标 {} -> {} 并行流式差值计算失败: {}", sourceIndicator, targetIndicator, e.getMessage(), e);
                    }
                });

        log.debug("并行流式差值计算完成，处理指标数: {}", propMapping.size());
    }






    /**
     * 并行流式差值计算（统一高性能版本）
     * 使用并行流式处理，简化代码并提升性能
     *
     * @param sourceData 源指标的时间序列数据
     * @param targetIndicator 目标差值指标名称
     * @param globalResults 全局结果映射
     */
    private void calculateDiffForIndicatorStream(
            ConcurrentHashMap<String, BigDecimal> sourceData,
            String targetIndicator,
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResults) {

        if (sourceData.size() < 2) {
            log.debug("指标 {} 数据点少于2个，无法计算差值", targetIndicator);
            return;
        }

        // 创建目标指标的数据映射
        ConcurrentHashMap<String, BigDecimal> targetData = globalResults.computeIfAbsent(
                targetIndicator, k -> new ConcurrentHashMap<>());

        // 使用并行流式处理：排序 → 转数组 → 并行计算差值
        String[] timeStamps = sourceData.keySet().parallelStream()
                .sorted()
                .toArray(String[]::new);

        // 并行计算相邻时间点的差值
        IntStream.range(0, timeStamps.length - 1)
                .parallel()
                .forEach(i -> {
                    String currentTime = timeStamps[i];
                    String nextTime = timeStamps[i + 1];

                    BigDecimal currentValue = sourceData.get(currentTime);
                    BigDecimal nextValue = sourceData.get(nextTime);

                    if (currentValue != null && nextValue != null) {
                        try {
                            // 计算差值：下一个值 - 当前值
                            BigDecimal diff = nextValue.subtract(currentValue);
                            // 将差值存储到当前时间点
                            targetData.put(currentTime, diff);
                        } catch (Exception e) {
                            log.warn("时间点 {} 差值计算失败: {}", currentTime, e.getMessage());
                        }
                    }
                });

        log.debug("指标 {} 并行流式差值计算完成，生成差值数据点数: {}", targetIndicator, targetData.size());
    }

}
