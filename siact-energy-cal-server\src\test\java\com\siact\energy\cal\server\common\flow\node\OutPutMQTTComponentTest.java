package com.siact.energy.cal.server.common.flow.node;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.enums.ConstantBase;
import com.siact.energy.cal.common.pojo.enums.DbTypeEnum;
import com.siact.energy.cal.common.util.utils.DBConnection;
import com.siact.energy.cal.common.util.utils.DBTools;
import com.siact.energy.cal.common.util.utils.MapToDbUtils;
import com.siact.energy.cal.common.util.utils.UUIDUtils;
import com.siact.energy.cal.server.dao.flow.FlowTableMapper;
import com.siact.energy.cal.server.dao.flow.SiComRelationMapper;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;

class OutPutMQTTComponentTest {

    @Resource
    FlowTableMapper flowTableMapper;

    @Autowired
    RedisUtil redisUtil;
    @Test
    void test() throws Exception{

        String broker = "tcp://**************:1883"; // MQTT代理的地址
        String clientId = "mqtt_java"; // 客户端ID
        MemoryPersistence persistence = new MemoryPersistence(); // 设置持久化
        try {
            MqttClient sampleClient = new MqttClient(broker, clientId, persistence);
            MqttConnectOptions connOpts = new MqttConnectOptions(); // 设置连接选项
            connOpts.setCleanSession(true); // 设置会话清除标志
            System.out.println("Connecting to broker: " + broker);
            connOpts.setUserName("mqqtuser");
            connOpts.setPassword("mqqtuser".toCharArray());
            sampleClient.connect(connOpts); // 连接到MQTT代理
            System.out.println("Connected");


            // 订阅MQTT FX的Topic
            String topic = "home/garden/fountain";
            int qos = 1;
            sampleClient.subscribe(topic, qos);
            System.out.println("Subscribed to topic: " + topic);

            // 接收来自MQTT FX的消息
            sampleClient.setCallback(new MqttCallback() {
                @Override
                public void messageArrived(String topic, MqttMessage message) throws Exception {
                    System.out.println("Received message: " + new String(message.getPayload()));

                    String msg = new String(message.getPayload());
                    JSONObject jsonObject = JSONUtil.parseObj(msg);
                    Set<String> keys = jsonObject.keySet();
                    //创建表

                    String tableId = UUIDUtils.uuidStdTableName();
                    StringBuilder sb = new StringBuilder();
                    sb.append("create table ").append(tableId).append("(");
                    for (String field : keys) {
                        sb.append(field).append(" text ,");
                    }
                    sb.deleteCharAt(sb.lastIndexOf(","));
                    sb.append(")");
                    Connection connection = null;
                    try {
                        connection = DBConnection.connection("mysql", "192.100.30.103",
                                "3306", "energy_cal", "root",
                                "123456");
                        DBTools.executeSql(connection, sb.toString());
                        //插入数据
                        //flowTableMapper.saveItemInput(tableId, mapType);

                       // String sql = "insert into " + tableId + " values(";
                        StringBuilder sql = new StringBuilder();
                        sql.append("insert into ").append(tableId).append(" values(");
                        for ( String key : keys) {

                            sql.append("'").append(jsonObject.get(key)).append("',");
                        }
                        sql.deleteCharAt(sql.lastIndexOf(","));
                        sql.append(")");
                        DBTools.executeSql(connection, sql.toString());
                    }catch (Exception e){
                        System.out.println("sql执行失败");
                        throw new BizException("DataSourceNode：sql执行失败");
                    }finally {
                        DBConnection.close(connection);
                    }

                }

                @Override
                public void connectionLost(Throwable cause) {
                    System.out.println("Connection lost: " + cause.getMessage());
                }

                @Override
                public void deliveryComplete(IMqttDeliveryToken token) {}
            });
            Thread.sleep(10000);

        } catch (MqttException me) {
            System.out.println("reason " + me.getReasonCode());
            System.out.println("msg " + me.getMessage());
            System.out.println("loc " + me.getLocalizedMessage());
            System.out.println("cause " + me.getCause());
            System.out.println("excep " + me);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void test2(){
        Connection connection = null;
        String sql = "select * from rule_detail where id = 1";

        try {
            connection = DBConnection.connection("mysql", "192.100.30.103",
                    "3306", "energy_cal", "root",
                    "123456");
            DBTools.executeSql(connection, sql);

            String tableId = UUIDUtils.uuidStdTableName();

            DBTools.executeSql(connection,"DROP TABLE IF EXISTS table_0717001");
        }catch (Exception e){
            System.out.println("sql执行失败");
            throw new BizException("DataSourceNode：sql执行失败");
        }finally {
            DBConnection.close(connection);
        }
    }
    @Resource
    SiComRelationMapper siComRelationMapper;

    @Test
    public void test_tao(){

        String tableName = UUIDUtils.uuidStdTableName();
        Set<String> set = new TreeSet<>();
        set.add("value");
        MapToDbUtils.createTale(tableName, set);
        try {
            Connection con = DBConnection.connection(DbTypeEnum.TaoS.getDbType(), "121.36.3.28",
                    "29001", "test", "root",
                    "taosdata");

            String sb = "SELECT SUM(itemvalue) FROM test.xych WHERE  ts>='2024-06-01 00:00:00' and ts<='2024-06-10 00:00:00' INTERVAL(1d) FILL(PREV);";
            ResultSet rs = DBTools.executeQuerySql(con, sb);

            List<Map<String, Object>> list = new ArrayList<>();
            while (true){
                if (!rs.next()){
                    break;
                }
                HashMap<String, Object> map = new HashMap<>();
                map.put("ts", rs.getObject(1).toString());
                map.put("value", rs.getObject(2).toString());
                list.add(map);
            }
            MapToDbUtils.insertDbList(list, tableName);
            con.close();
        }catch (Exception e){

        }
    }

    @Test
    public void test3(){
        redisUtil.hset(ConstantBase.INS_REDISKEY, "PGY02001_SXNXT001_STXNZD1001_UXNDY1008_EQ000000000000_MPZYS2002" + ConstantBase.SPLITREDISKEY + 10000,10000);
//        Object hget = redisUtil.hget(ConstantBase.INS_REDISKEY, "PGY02001_SXNXT001_STXNZD1001_UXNDY1008_EQ000000000000_MPZYS2002" + ConstantBase.SPLITREDISKEY + 10000);
//        System.out.println(hget);
    }

}