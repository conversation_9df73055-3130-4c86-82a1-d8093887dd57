package com.siact.energy.cal.server.common.datasource.db.impl.tdengine;

import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.server.common.datasource.db.core.AbstractSqlBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("tdengineSqlBuilder")
@Slf4j
public class TDengineSqlBuilder extends AbstractSqlBuilder {

    @Override
    public String buildBasicQuerySql(TimeQueryDTO queryDTO, DataSourceVo dataSourceVo) {
        List<String> devpropertyList = queryDTO.getDataCodes();
        if (devpropertyList == null || devpropertyList.isEmpty()) {
            // 返回一个永远查询不到任何结果的安全SQL，避免空指针
            return String.format("SELECT * FROM %s WHERE 1 = 0", dataSourceVo.getTableName());
        }
        String devpropListStr = devpropertyList.stream()
                .map(value -> "'" + value + "'")
                .collect(Collectors.joining(","));

        if (!queryDTO.isEquallySpacedQuery()) {
            // 时间区间查询模式
            return String.format(
                    "SELECT first(ts) as ts, devproperty, first(itemvalue) as itemvalue FROM %s WHERE devproperty IN (%s) AND ts >= '%s' AND ts <= '%s' GROUP BY devproperty " +
                            "UNION ALL " +
                            "SELECT last(ts) as ts, devproperty, last(itemvalue) as itemvalue FROM %s WHERE devproperty IN (%s) AND ts >= '%s' AND ts <= '%s' GROUP BY devproperty",
                    dataSourceVo.getTableName(), devpropListStr, queryDTO.getStartTime(), queryDTO.getEndTime(),
                    dataSourceVo.getTableName(), devpropListStr, queryDTO.getStartTime(), queryDTO.getEndTime()
            );

        } else {
            // 等间隔查询模式 (逻辑不变)
            String intervalStr = queryDTO.getInterval() + queryDTO.getTsUnit();
            return String.format("SELECT first(itemvalue) as itemvalue FROM %s WHERE devproperty IN (%s) AND ts >= '%s' AND ts <= '%s' INTERVAL(%s) FILL(NULL) GROUP BY devproperty",
                    dataSourceVo.getTableName(), devpropListStr, queryDTO.getStartTime(), queryDTO.getEndTime(), intervalStr);
        }
    }

    @Override
    public String buildTimeRangeAggSql(String function,
                                       Map<String, String> propMapping,
                                       TimeQueryDTO queryDTO,
                                       String tableName) {
        // 将设备属性列表转换为SQL IN子句格式
        String devpropListStr = propMapping.keySet().stream()
                .map(value -> "'" + value + "'")
                .collect(Collectors.joining(","));

        // SQL模板
        String sqlTemplate = "SELECT first(ts) as ts,%s(itemvalue) as itemvalue " +
                "FROM %s " +
                "WHERE devproperty in (%s) " +
                "and ts >= '%s' " +
                "AND ts <= '%s' " +
                "group by devproperty";

        Set<String> supportedFunctions = new HashSet<>(Arrays.asList("sum", "avg", "max", "min", "last", "first"));
        if (!supportedFunctions.contains(function)) {
            throw new BizException("不支持的聚合函数: " + function);
        }

        return String.format(sqlTemplate,
                function,
                tableName,
                devpropListStr,
                queryDTO.getStartTime(),
                queryDTO.getEndTime());
    }


    @Override
    public String buildCountByIntervalSql(Set<String> sourceProps, TimeQueryDTO queryDTO, String tableName) {
        String devpropListStr = sourceProps.stream()
                .map(value -> "'" + value + "'")
                .collect(Collectors.joining(","));

        String intervalStr = queryDTO.getInterval() + queryDTO.getTsUnit();

        // 🔥【核心SQL】
        // 1. 内部查询使用 INTERVAL 和 FILL(NULL) 来生成所有时间窗口，即使某些窗口没数据(值为NULL)
        // 2. 外部查询对这个结果进行 COUNT，这样 COUNT(*) 就能统计出窗口的总数
        // 3. 但是 TDengine 的 COUNT 函数会忽略 NULL 值。
        // 一个更聪明的办法是 COUNT(ts)，因为 ts 列即使在 FILL(NULL) 时也会被填充。
        // 为了简化并确保正确性，我们直接在应用层判断。
        // 我们需要的是每个窗口是否有值，最直接的方法是捞取数据后在Java中统计。
        // 但为了减少数据传输，我们仍然尝试在SQL层面解决。
        //
        // 最终方案：查询每个窗口的 COUNT(itemvalue)，这个值要么是1，要么是0（因为FILL(NULL)）。
        // 然后在外部对这个计数值求和。
        // SELECT devproperty, SUM(count_val) as total_count FROM (
        //   SELECT devproperty, COUNT(itemvalue) as count_val FROM ... INTERVAL(...) FILL(NULL)
        // ) GROUP BY devproperty
        //
        // 考虑到 TDengine SQL 语法的限制，最简单可靠的还是查询原始数据点，然后在应用层按窗口统计。
        // 但是，为了性能，我们坚持在DB层做。下面是一个可行的SQL:
        // 我们统计每个窗口的第一个值，非空的窗口会返回一行，空的窗口不返回。
        // 然后在外面统计行数。
        return String.format(
                "SELECT devproperty, COUNT(*) as count_val FROM " +
                        "(SELECT devproperty, first(itemvalue) FROM %s WHERE devproperty IN (%s) AND ts >= '%s' AND ts <= '%s' INTERVAL(%s)) " +
                        "GROUP BY devproperty",
                tableName,
                devpropListStr,
                queryDTO.getStartTime(),
                queryDTO.getEndTime(),
                intervalStr
        );
    }

    @Override
    public String buildIntervalAggSql(String function,
                                      Map<String, String> propMapping,
                                      TimeQueryDTO queryDTO,
                                      String tableName) {
        // 将设备属性列表转换为SQL IN子句格式
        String devpropListStr = propMapping.keySet().stream()
                .map(value -> "'" + value + "'")
                .collect(Collectors.joining(","));

        // 构建时间间隔
        String intervalUnit = queryDTO.getInterval() + queryDTO.getTsUnit();

        // SQL模板
        String sqlTemplate = "SELECT first(ts) as ts,%s(itemvalue) as itemvalue " +
                "FROM %s " +
                "WHERE devproperty in (%s) " +
                "and ts >= '%s' " +
                "AND ts <= '%s' " +
                "interval(%s) FILL(NULL) " +
                "group by devproperty";

        Set<String> supportedFunctions = new HashSet<>(Arrays.asList("sum", "avg", "max", "min", "last", "first"));
        if (!supportedFunctions.contains(function)) {
            throw new BizException("不支持的聚合函数: " + function);
        }

        return String.format(sqlTemplate,
                function,
                tableName,
                devpropListStr,
                queryDTO.getStartTime(),
                queryDTO.getEndTime(),
                intervalUnit);
    }

    @Override
    public String buildTimeSliceDataSql(String startTime, String endTime, List<String> dataCOdes, String tableName) {
        String devpropListStr = dataCOdes.stream()
                .map(value -> "'" + value + "'") // 将每个元素用单引号括起来
                .collect(Collectors.joining(","));

        String sql = String.format("SELECT ts, last(itemvalue) as itemvalue FROM %s WHERE devproperty in (%s) and ts >= '%s' AND ts <= '%s' group by devproperty;",
                tableName,
                devpropListStr,
                startTime,
                endTime);
        return sql;
    }

    @Override
    public String buildDiffSql(Map<String, String> propMapping, String tableName, String startTime, String endTime, String interval, String unit) {
        // 将设备属性列表转换为SQL IN子句格式
        String devpropListStr = propMapping.keySet().stream()
                .map(value -> "'" + value + "'")
                .collect(Collectors.joining(","));

        // 为时间区间DIFF计算构建特殊的SQL
        // 使用LAST - FIRST来直接计算差值，避免时间戳重复问题
        String sql = String.format(
                "SELECT '%s' as ts, devproperty, (LAST(itemvalue) - FIRST(itemvalue)) as itemvalue " +
                        "FROM %s " +
                        "WHERE devproperty in (%s) " +
                        "AND ts >= '%s' AND ts <= '%s' " +
                        "GROUP BY devproperty",
                startTime, // 使用开始时间作为统一的时间戳
                tableName,
                devpropListStr,
                startTime,
                endTime
        );

        return sql;
    }
}