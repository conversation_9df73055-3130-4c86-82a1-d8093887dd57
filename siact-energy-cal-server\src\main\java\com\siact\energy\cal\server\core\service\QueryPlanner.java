package com.siact.energy.cal.server.core.service;

import com.google.common.collect.Lists;
import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.server.common.config.CalculationProperties;
import com.siact.energy.cal.server.common.config.IOThreadPoolConfig;
import com.siact.energy.cal.server.core.pojo.TimeWindow;
import com.siact.energy.cal.server.core.utils.TimeWindowGenerator;
import com.siact.energy.cal.server.service.energycal.DataBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 智能查询规划器
 * <p>
 * 负责接收大型查询任务，根据预设的成本阈值，
 * 智能地将其分解为多个可控的、可并行执行的子任务，
 * 以解决查询超时和数据库压力过大的问题。
 */
@Service
@Slf4j
public class QueryPlanner {

    @Autowired
    private CalculationProperties props;
    @Autowired
    private TimeWindowGenerator timeWindowGenerator;
    @Autowired
    private DataBaseService dataBaseService;
    private final ForkJoinPool calculationPool = new ForkJoinPool(Math.min(Runtime.getRuntime().availableProcessors() * 2, 32));

    /**
     * 执行查询计划的主入口。
     *
     * @param indicators  需要查询的指标列表
     * @param queryDTO    原始的查询请求
     * @param dataSourceVo 数据源信息
     * @return 汇总后的查询结果
     */
    public ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> executePlan(
            List<String> indicators,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo) {

        // 1. 制定分片计划
        List<TimeQueryDTO> subTasks;
        String planDescription;

        if (queryDTO.isEquallySpacedQuery()) {
            long days = TimeWindowGenerator.getDaysBetween(queryDTO.getStartTime(), queryDTO.getEndTime());
            boolean needsTimeSharding = days > props.getTimeShardThresholdDays();
            boolean needsMetricSharding = indicators.size() > props.getMetricShardThresholdCount();
            planDescription = buildPlanDescription("等时间间隔", needsTimeSharding, needsMetricSharding, days, indicators.size());
            subTasks = breakdownForEqualInterval(indicators, queryDTO, props, needsTimeSharding, needsMetricSharding);
        } else {
            boolean needsMetricSharding = indicators.size() > props.getMetricShardThresholdCount();
            planDescription = buildPlanDescription("时间区间采样", false, needsMetricSharding, 0, indicators.size());
            subTasks = breakdownForIntervalSampling(indicators, queryDTO, props, needsMetricSharding);
        }

        if (subTasks.size() > 1) {
            log.info("【查询计划已制定】原始任务将被分解为 {} 个子任务。{}", subTasks.size(), planDescription);
        } else {
            log.info("【查询计划已制定】任务无需分片，将直接执行。{}", planDescription);
        }

        // 2. 并行调度与执行
        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> globalResultMap = new ConcurrentHashMap<>();
        Semaphore semaphore = new Semaphore(props.getMaxConcurrentQueries());

        List<CompletableFuture<Void>> futures = subTasks.stream().map(subTask ->
            CompletableFuture.runAsync(() -> {
                try {
                    semaphore.acquire();
                    log.debug("执行子任务: 时间[{} -> {}], 指标数[{}]",
                        subTask.getStartTime(), subTask.getEndTime(), subTask.getDataCodes().size());
                    // 调用底层的 DataBaseService 查询方法
                    dataBaseService.queryDataByEquallySpacedTime(globalResultMap, dataSourceVo, subTask, subTask.getDataCodes());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("子任务在等待信号量时被中断", e);
                } catch (Exception e) {
                    log.error("子任务执行失败: 时间[{} -> {}], 指标数[{}]",
                        subTask.getStartTime(), subTask.getEndTime(), subTask.getDataCodes().size(), e);
                } finally {
                    semaphore.release();
                }
            }, calculationPool)
        ).collect(Collectors.toList());

        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            log.warn("部分查询子任务执行失败，结果可能不完整。", e);
        }
        
        return globalResultMap;
    }


    /**
     * 【新增】执行一个“数据完整性检查”的查询计划。
     * <p>
     * 它会智能地分片执行 COUNT 查询，并在内存中汇总结果。
     *
     * @param indicators  需要检查的指标列表
     * @param queryDTO    定义了时间范围和检查粒度 (interval/tsUnit)
     * @param dataSourceVo 数据源信息
     * @return 一个 Map，Key是指标编码，Value是该指标在总时间范围内的总数据点数。
     */
    public Map<String, Long> executeCountPlan(
            List<String> indicators,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo) {

        // 1. 制定分片计划（与等间隔查询的分片逻辑完全相同）
        long days = TimeWindowGenerator.getDaysBetween(queryDTO.getStartTime(), queryDTO.getEndTime());
        boolean needsTimeSharding = days > props.getTimeShardThresholdDays();
        boolean needsMetricSharding = indicators.size() > props.getMetricShardThresholdCount();

        List<TimeQueryDTO> subTasks = breakdownForEqualInterval(indicators, queryDTO, props, needsTimeSharding, needsMetricSharding);

        log.info("【计数计划已制定】完整性检查任务被分解为 {} 个子任务。", subTasks.size());

        // 2. 并行调度与执行
        ConcurrentHashMap<String, Long> globalCounts = new ConcurrentHashMap<>();
        Semaphore semaphore = new Semaphore(props.getMaxConcurrentQueries());

        List<CompletableFuture<Void>> futures = subTasks.stream().map(subTask ->
                CompletableFuture.runAsync(() -> {
                    try {
                        semaphore.acquire();
                        // a. 调用 DataBaseService 执行单个分片的 COUNT 查询
                        Map<String, Long> shardCounts = dataBaseService.queryCountsForShard(subTask, dataSourceVo);

                        // b. 🔥【核心】在内存中进行二次汇总 (线程安全)
                        shardCounts.forEach((indicator, count) ->
                                globalCounts.merge(indicator, count, Long::sum)
                        );
                    } catch (Exception e) {
                        log.error("计数子任务执行失败", e);
                    } finally {
                        semaphore.release();
                    }
                }, calculationPool)
        ).collect(Collectors.toList());

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        return globalCounts;
    }

    /**
     * 分解策略一：为“等时间间隔”查询进行分片 (可同时按时间和指标)
     */
    private List<TimeQueryDTO> breakdownForEqualInterval(
            List<String> indicators,
            TimeQueryDTO dto,
            CalculationProperties props,
            boolean needsTimeSharding,
            boolean needsMetricSharding) {

        List<TimeWindow> timeShards;
        if (needsTimeSharding) {
            long days = TimeWindowGenerator.getDaysBetween(dto.getStartTime(), dto.getEndTime());
            String shardUnit = determineTimeShardUnit(days);
            timeShards = timeWindowGenerator.generateShards(dto.getStartTime(), dto.getEndTime(), 1, shardUnit);
        } else {
            timeShards = Collections.singletonList(new TimeWindow(dto.getStartTime(), dto.getEndTime(), null));
        }

        List<List<String>> metricShards;
        if (needsMetricSharding) {
            metricShards = Lists.partition(indicators, props.getMetricShardSize());
        } else {
            metricShards = Collections.singletonList(indicators);
        }

        List<TimeQueryDTO> subTasks = new ArrayList<>();
        for (TimeWindow shard : timeShards) {
            for (List<String> metricGroup : metricShards) {
                TimeQueryDTO subTaskDTO = new TimeQueryDTO();
                BeanUtils.copyProperties(dto, subTaskDTO);
                subTaskDTO.setStartTime(shard.getStartTime());
                subTaskDTO.setEndTime(shard.getEndTime());
                subTaskDTO.setDataCodes(metricGroup);
                subTasks.add(subTaskDTO);
            }
        }
        return subTasks;
    }

    /**
     * 分解策略二：为“时间区间采样 (FIRST/LAST)”查询进行分片 (只能按指标)
     */
    private List<TimeQueryDTO> breakdownForIntervalSampling(
            List<String> indicators,
            TimeQueryDTO dto,
            CalculationProperties props,
            boolean needsMetricSharding) {
        
        if (needsMetricSharding) {
            List<List<String>> metricShards = Lists.partition(indicators, props.getMetricShardSize());
            return metricShards.stream().map(group -> {
                TimeQueryDTO subTaskDTO = new TimeQueryDTO();
                BeanUtils.copyProperties(dto, subTaskDTO);
                subTaskDTO.setDataCodes(group);
                // 时间范围保持为完整的原始范围
                return subTaskDTO;
            }).collect(Collectors.toList());
        } else {
            dto.setDataCodes(indicators);
            return Collections.singletonList(dto);
        }
    }

    /**
     * 构建清晰的计划描述日志
     */
    private String buildPlanDescription(
            String queryType,
            boolean timeSharding,
            boolean metricSharding,
            long days,
            int metricCount) {
                
        StringBuilder sb = new StringBuilder();
        sb.append("查询类型: [").append(queryType).append("]。");
        sb.append(" 原始负载: [").append(metricCount).append("个指标, ").append(days).append("天]。");
        
        List<String> actions = new ArrayList<>();
        if (timeSharding) {
            actions.add("按 [时间] (粒度: " + determineTimeShardUnit(days) + ") 分片");
        }
        if (metricSharding) {
            actions.add("按 [指标] (每批 " + props.getMetricShardSize() + " 个) 分片");
        }
        
        if (actions.isEmpty()) {
            sb.append(" 分片策略: [无]。");
        } else {
            sb.append(" 分片策略: [").append(String.join(", ", actions)).append("]。");
        }
        return sb.toString();
    }
    
    /**
     * 智能决策时间分片粒度
     */
    private String determineTimeShardUnit(long days) {
        if (days <= 31) return "天(d)";
        if (days <= 180) return "周(w)";
        return "月(M)";
    }
}