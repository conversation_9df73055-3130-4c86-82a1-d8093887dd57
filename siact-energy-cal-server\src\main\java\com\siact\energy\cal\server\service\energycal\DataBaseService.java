package com.siact.energy.cal.server.service.energycal;/**
 * @Package com.siact.energy.cal.server.service.energycal
 * @description: 数据库服务
 * <AUTHOR>
 * @create 2024/12/5 16:53
 */

import com.google.common.collect.Lists;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.common.util.utils.DateUtils;
import com.siact.energy.cal.server.common.config.IOThreadPoolConfig;
import com.siact.energy.cal.server.common.datasource.db.core.AbstractDbOperator;
import com.siact.energy.cal.server.common.datasource.db.core.AbstractSqlBuilder;
import com.siact.energy.cal.server.common.datasource.db.factory.TimeSeriesDbFactory;
import com.siact.energy.cal.server.common.utils.RedisUtil;
import com.siact.energy.cal.server.core.pojo.TimeWindow;
import com.siact.energy.cal.server.core.service.QueryPlanner;
import com.siact.energy.cal.server.core.utils.TimeWindowGenerator;
import com.siact.energy.cal.server.service.ruleDetail.impl.RuleDetailServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ClassName DataBaseService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/5 16:53
 * @Version 1.0
 **/
@Service
@Slf4j
public class DataBaseService {

    // 使用正则表达式匹配计算函数，例如sum(@[A])
    private static final Pattern FUNCTION_PATTERN = Pattern.compile("(\\w+)\\(@\\[(\\w+)\\]\\)");
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    // 单次查询的最大设备数量
    private static final int BATCH_SIZE = 100;
    private static final int MAX_UNION_COUNT = 20; // 每个SQL批次的最大查询数量
    private static final int QUERY_TIMEOUT_SECONDS = 20;
    @Autowired
    @Qualifier(IOThreadPoolConfig.IO_THREAD_POOL_NAME)
    private Executor executor;
    @Autowired
    private RedisUtil redisUtil;
    @Value("${digitalTwin.api.url}")
    private String dataTwinsUrl;
    @Autowired
    private RuleDetailServiceImpl ruleDetailService;
    @Autowired
    private TimeSeriesDbFactory timeSeriesDbFactory;
    @Autowired
    private TimeWindowGenerator timeWindowGenerator;
    @Autowired
    private QueryPlanner queryPlanner;

    /**
     * 查询等间隔时序数据（入口方法）
     */
    public void queryDataByEquallySpacedTime(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            DataSourceVo dataSourceVo,
            TimeQueryDTO queryDTO,
            List<String> allDataCodes) {

        try {
            // 如果设备数量超过阈值，进行分批查询
            if (allDataCodes.size() > BATCH_SIZE) {
                batchQuery(resultMap, dataSourceVo, queryDTO, allDataCodes);
            } else {
                queryDTO.setDataCodes(allDataCodes);
                singleQuery(resultMap, dataSourceVo, queryDTO);
            }
        } catch (Exception e) {
            log.error("Error querying time series data: {}", e.getMessage(), e);
            throw new BizException("查询时序数据异常", e);
        }
    }



    /**
     * 【重构版】检查并返回数据点数不足的指标列表。
     */
    public List<String> findIncompleteIndicators(
            Set<String> sourceProps,
            List<TimeWindow> timeWindows,
            TimeQueryDTO queryDTO,
            DataSourceVo dataSourceVo) {

        if (sourceProps.isEmpty() || CollectionUtils.isEmpty(timeWindows)) {
            return Collections.emptyList();
        }

        // 1. 🔥 调用 QueryPlanner 来高效地获取所有指标的总点数
        Map<String, Long> actualCounts = queryPlanner.executeCountPlan(
                new ArrayList<>(sourceProps), queryDTO, dataSourceVo
        );

        // 2. 在内存中进行比较
        long expectedCount = timeWindows.size();
        List<String> incompleteIndicators = new ArrayList<>();
        for (String prop : sourceProps) {
            long actualCount = actualCounts.getOrDefault(prop, 0L);
            if (actualCount < expectedCount) {
                log.warn("数据完整性检查：指标 [{}] 期望点数 {}, 实际点数 {}，需要补算。", prop, expectedCount, actualCount);
                incompleteIndicators.add(prop);
            }
        }
        return incompleteIndicators;
    }

    /**
     * 【新增】查询单个分片的数据点数，供 QueryPlanner 调用。
     */
    public Map<String, Long> queryCountsForShard(TimeQueryDTO queryDTO, DataSourceVo dataSourceVo) {
        Integer dbType = dataSourceVo.getDbType();
        AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);

        // a. 构建用于统计点数的SQL
        String sql = sqlBuilder.buildCountByIntervalSql(new HashSet<>(queryDTO.getDataCodes()), queryDTO, dataSourceVo.getTableName());

        // b. 执行查询并获取该分片内每个指标的实际点数
        return dbOperator.executeCountQuery(sql, dataSourceVo);
    }
    /**
     * 分批查询处理
     */
    private void batchQuery(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            DataSourceVo dataSourceVo,
            TimeQueryDTO queryDTO,
            List<String> allDataCodes) {
        // 分批调用并发调用
        List<CompletableFuture<Void>> futureList = Lists.newArrayList();
        // 将设备列表分批
        List<List<String>> batches = Lists.partition(allDataCodes, BATCH_SIZE);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("开始批量查询基础指标");
        // 创建所有批次的查询任务
        batches.forEach(batch -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                TimeQueryDTO batchQueryDTO = copyQueryDTO(queryDTO);
                batchQueryDTO.setDataCodes(batch);
                singleQuery(resultMap, dataSourceVo, batchQueryDTO);
            }, executor);
            futureList.add(future);
        });

        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[]{})).join();
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        log.info("批量查询基础指标耗时：{} ms", stopWatch.getTotalTimeMillis());
    }

    /**
     * 单批次查询处理
     */
    private void singleQuery(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            DataSourceVo dataSourceVo,
            TimeQueryDTO queryDTO) {
        Integer dbType = dataSourceVo.getDbType();
        AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
        String sql = sqlBuilder.buildBasicQuerySql(queryDTO, dataSourceVo);
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
        dbOperator.executeBasicSql(resultMap, sql, dataSourceVo);
    }

    private TimeQueryDTO copyQueryDTO(TimeQueryDTO original) {
        TimeQueryDTO copy = new TimeQueryDTO();
        BeanUtils.copyProperties(original, copy);
        return copy;
    }

    public void processDiffQuery(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, Map<String, String> propMapping, TimeQueryDTO queryDTO, DataSourceVo dataSourceVo) {
        Integer dbType = dataSourceVo.getDbType();
        //先组装sql
        AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
        //这里查询的应该是聚合指标的源指标，也就是proMapping中的values
        queryDTO.setDataCodes(propMapping.keySet().stream().collect(Collectors.toList()));
        String sql = sqlBuilder.buildBasicQuerySql(queryDTO, dataSourceVo);
        //查询sql
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
        dbOperator.executeDiffQuery(resultMap, sql, dataSourceVo, propMapping);
    }

    public void processAggQuery(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, String functionName, Map<String, String> propMapping, TimeQueryDTO queryDTO, DataSourceVo dataSourceVo) {
        Integer dbType = dataSourceVo.getDbType();
        AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
        String sql = "";
        if(queryDTO.isEquallySpacedQuery()){
            sql = sqlBuilder.buildIntervalAggSql(functionName, propMapping, queryDTO, dataSourceVo.getTableName());
        }else{
            sql = sqlBuilder.buildTimeRangeAggSql(functionName, propMapping, queryDTO, dataSourceVo.getTableName());
        }
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
        dbOperator.executeAggQuery(resultMap, sql, dataSourceVo, propMapping);
    }



 /*   public void processRangeAggQuery(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, String functionName, Map<String, String> propMapping, TimeQueryDTO queryDTO, DataSourceVo dataSourceVo) {
        Integer dbType = dataSourceVo.getDbType();
        AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
        String sql = sqlBuilder.buildTimeRangeAggSql(functionName, propMapping, queryDTO, dataSourceVo.getTableName());
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
        dbOperator.executeAggQuery(resultMap, sql, dataSourceVo, propMapping);

    }*/

    /*
     * <AUTHOR>
     * @Description //时间切面查询
     * @Date 17:03 2024/12/9
     * @Param
     * @return
     **/
    public void queryTimeSliceData(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, DataSourceVo dataSourceVo, TimeQueryDTO queryDTO, List<String> dataCodes) {

        String ts = queryDTO.getStartTime();
        String stime = DateUtils.longToStr(DateUtils.parseDate(ts).getTime() - 1000 * 60 * 15);
        String etime = ts;
        Integer dbType = dataSourceVo.getDbType();
        AbstractSqlBuilder sqlBuilder = timeSeriesDbFactory.getSqlBuilder(dbType);
        String sql = sqlBuilder.buildTimeSliceDataSql(stime, etime, dataCodes, dataSourceVo.getTableName());
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
        dbOperator.executeBasicSql(resultMap, sql, dataSourceVo);
    }


    public void insertData(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, List<String> dataCodes, DataSourceVo dataSourceVo) {
        Integer dbType = dataSourceVo.getDbType();
        AbstractDbOperator dbOperator = timeSeriesDbFactory.getDbOperator(dbType);
        dbOperator.insertData(resultMap, dataCodes, dataSourceVo);
    }
}
